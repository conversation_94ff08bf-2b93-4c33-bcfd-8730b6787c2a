我有一个数据库，里面2张表需要进行数据迁移，从tiku表（后面称为A表）将数据迁移到hook_question_bank表（后面称为B表），写一个脚本帮我完成。

zhi ding


数据库的配置信息为；

# ===========================================
# 数据库配置 (MySQL)
# ===========================================
DB_HOST=***********
DB_PORT=3380
DB_USERNAME=gmdns
DB_PASSWORD=Suyan15913..
DB_DATABASE=solve_web
DB_CHARSET=utf8mb4



业务要求；

1. A表的title字段 迁移到B表的 content字段
2. A表的 option_a、option_b、option_c、option_d、option_e字段 迁移到B表的 options字段
    - 注意b表的json格式，option_d = Y正确 ，option_e = N 错误
    - B表选择题示例：{"A": "左侧A柱盲区内可能有行人将要通过", "B": "对向车道车辆将要调头", "C": "后面有车辆将超车", "D": "右侧车道有车辆将要通过"}
    - B表判断题示例：{"N": "错误", "Y": "正确"}
    - 另外需要注意的是，A表中的选项的值，需要进行一次正则清理，清理掉标点符号后在对应存入。处理方法如下；
        - // 清理所有标点符号和空格
            punctRe := regexp.MustCompile(`[[:punct:]\s，。？！；：""''（）【】《》、]+`)

3. A表的 answer字段 迁移到B表的 answer字段
    - 需要注意A表中的answer字段只有选项键没有选项值，需要将正确答案对应的选项值取出来存入
    - B表 多选题正确答案示例；{"B": "提前开启左转向灯", "D": "在掉头车道掉头"}
    - B表 单选题正确答案示例；{"A": "左侧A柱盲区内可能有行人将要通过"}
    - B表 判断题正确答案示例；{"Y": "正确"}


4. A表中category字段的1=单选题，2=多选题，3=判断题 B表的type字段的1=判断题，2=单选题，3=多选题，需要做好映射存入。

5. B表中的content_clean字段，是使用A表中的title字段处理后得到的，处理方法如下；
        - // 第一步：清除题目前缀
            re := regexp.MustCompile(`^(（?\(?\s*(单选题|多选题|判断题|填空题)?\)?）?\s*\d{1,3}[、.．]?\s*)`)

        // 第二步：清理所有标点符号和空格
            punctRe := regexp.MustCompile(`[[:punct:]\s，。？！；：""''（）【】《》、]+`)

6. B表中的hash_key字段，是使用5.中处理后的content_clean字段进行MD5哈希得到的，处理方法如下；
    - func (s *ParserService) generateHashKey(content string) string {
            hash := md5.Sum([]byte(content))      // MD5哈希计算
            return fmt.Sprintf("%x", hash)        // 转换为十六进制字符串
        }

7. B表中的user_url字段统一使用下面的地址存入，统一用一个即可。
    - http://img.igmdns.com/images/cc0143.jpg

8. B表中的verified字段统一为1

9. B表中的question_len字段是将content_clean字段的值计算字符长度存入的字符长度值

10. A表中的image_url字段迁移到B表的image_url字段，需要进行处理，方法如下
    - 使用固定的 https://tiku.uzdns.com/storage/ + image_url字段的值，组成完整的图片url。
